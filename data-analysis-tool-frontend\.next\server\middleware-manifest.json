{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_2c2f7f9b._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_9e9e701a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|images|fonts|api).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|images|fonts|api).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "vxTVhA8XalJO92PbSKJ7lk2zRCVwDY/NQi5gp+1+9f4=", "__NEXT_PREVIEW_MODE_ID": "928accbe28e2a6de5263725f2c8c1a8c", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "5478b70b7bbeb7247fa411e5e8c456fc125226e89efcbc5dca7e8a92e5798be4", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "35eb4c22633d5f96c7e0686dacb8ec3bcf95b50f8b3103996648b89d9bac0cc5"}}}, "sortedMiddleware": ["/"], "functions": {}}