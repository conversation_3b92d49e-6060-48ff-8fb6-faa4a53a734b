"use client";

import React, { useState, useEffect } from "react";
import { Question, QuestionGroup } from "@/types/formBuilder";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  X,
  ChevronLeft,
  Calendar,
  Clock,
  ChevronDown,
  ChevronRight,
} from "lucide-react";
import { TableInput } from "@/components/form-inputs/TableInput";
import {
  getVisibleQuestions,
  cleanupHiddenAnswers,
  validateVisibleQuestions,
  getNestedQuestions,
} from "@/lib/conditionalQuestions";
import NestedQuestionRenderer from "@/components/form-inputs/NestedQuestionRenderer";
import {
  buildNestedGroups,
  createUnifiedFormItems,
  getUngroupedQuestions,
  initializeGroupExpansionState
} from "@/lib/utils/nestedGroups";
import { ContextType } from "@/types";
import { useTranslations } from "next-intl";

interface FormPreviewProps {
  questions: Question[];
  questionGroups?: QuestionGroup[];
  contextType?: ContextType;
  onClose: () => void;
  hashedId?: string; // Add hashedId prop to pass to the test form
}

export function FormPreview({
  questions,
  questionGroups = [],
  contextType = "project",
  onClose,
  hashedId,
}: FormPreviewProps) {
  const [answers, setAnswers] = useState<Record<string, any>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [visibleQuestions, setVisibleQuestions] = useState<Question[]>([]);
  const [nestedQuestions, setNestedQuestions] = useState<
    Array<{
      question: Question;
      isVisible: boolean;
      isFollowUp: boolean;
      followUps: Array<{
        question: Question;
        isVisible: boolean;
      }>;
    }>
  >([]);
  const [expandedGroups, setExpandedGroups] = useState<Record<number, boolean>>(
    {}
  );

  const t = useTranslations();

  useEffect(() => {
    const initialAnswers: Record<string, any> = {};
    questions.forEach((question) => {
      initialAnswers[question.id] =
        question.inputType === "selectmany" ? [] : "";
    });
    setAnswers(initialAnswers);
  }, [questions]);

  // Update visible questions when answers or questions change
  useEffect(() => {
    if (questions) {
      const newVisibleQuestions = getVisibleQuestions(questions, answers);
      setVisibleQuestions(newVisibleQuestions);

      // Calculate nested question structure
      const newNestedQuestions = getNestedQuestions(questions, answers);
      setNestedQuestions(newNestedQuestions);

      // Clean up answers for questions that are no longer visible
      const cleanedAnswers = cleanupHiddenAnswers(answers, newVisibleQuestions);
      if (Object.keys(cleanedAnswers).length !== Object.keys(answers).length) {
        setAnswers(cleanedAnswers);
      }
    }
  }, [questions, answers]);

  // Build nested group structure
  const nestedQuestionGroups = React.useMemo(() => {
    return buildNestedGroups(questionGroups, questions);
  }, [questionGroups, questions]);

  // Get ungrouped questions
  const ungroupedQuestions = React.useMemo(() => {
    return getUngroupedQuestions(questions);
  }, [questions]);

  // Initialize all groups (including nested ones) as expanded
  useEffect(() => {
    const initialExpandedState = initializeGroupExpansionState(nestedQuestionGroups, true);
    setExpandedGroups(initialExpandedState);
  }, [nestedQuestionGroups]);

  // Create a unified list of form items (groups and individual questions) for dynamic ordering
  const unifiedFormItems = React.useMemo(() => {
    // Only add question groups for projects (not templates or question blocks)
    if (contextType === "project") {
      return createUnifiedFormItems(nestedQuestionGroups, ungroupedQuestions);
    } else {
      // For templates and question blocks, just return all questions
      return questions.map((question: Question) => ({
        type: "question" as const,
        data: question,
        order: question.position,
        originalPosition: question.position,
      }));
    }
  }, [nestedQuestionGroups, ungroupedQuestions, questions, contextType]);

  // Toggle group expansion
  const toggleGroupExpansion = (groupId: number) => {
    setExpandedGroups((prev) => ({
      ...prev,
      [groupId]: !prev[groupId],
    }));
  };

  const handleInputChange = (questionId: number, value: any) => {
    setAnswers((prev) => ({
      ...prev,
      [questionId]: value,
    }));
    setErrors((prev) => ({
      ...prev,
      [questionId]: "",
    }));
  };

  const validateForm = () => {
    // Only validate visible questions
    const newErrors = validateVisibleQuestions(visibleQuestions, answers);
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Render a single question with its input
  const renderQuestion = (question: Question) => (
    <div
      key={question.id}
      className="border border-neutral-500 dark:border-neutral-700 rounded-md p-4"
    >
      <div className="mb-2">
        <Label className="text-base font-medium">
          {question.label}
          {question.isRequired && <span className="text-red-500 ml-1">*</span>}
        </Label>
        {question.hint && (
          <p className="text-sm text-muted-foreground mt-1">{question.hint}</p>
        )}
        {errors[question.id] && (
          <p className="text-sm text-red-500 mt-1">{errors[question.id]}</p>
        )}
      </div>
      <div className="mt-2">{renderQuestionInput(question)}</div>
    </div>
  );

  const renderQuestionInput = (question: Question) => {
    const value =
      answers[question.id] ?? (question.inputType === "selectmany" ? [] : "");

    switch (question.inputType) {
      case "text":
        if (question.hint?.includes("multiline")) {
          return (
            <Textarea
              value={value}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                handleInputChange(question.id, e.target.value)
              }
              placeholder={question.hint || t('yourAnswer')}
              required={question.isRequired}
            />
          );
        }
        return (
          <Input
            value={value}
            onChange={(e) => handleInputChange(question.id, e.target.value)}
            placeholder={question.hint || t('yourAnswer')}
            required={question.isRequired}
          />
        );

      case "number":
        return (
          <Input
            type="number"
            value={value}
            onChange={(e) => handleInputChange(question.id, e.target.value)}
            placeholder={question.hint || t('yourAnswer')}
            required={question.isRequired}
          />
        );

      case "decimal":
        return (
          <Input
            type="number"
            step={"any"}
            value={value}
            onChange={(e) => handleInputChange(question.id, e.target.value)}
            placeholder={question.hint || t('yourAnswer')}
            required={question.isRequired}
          />
        );

      case "selectone":
        return (
          <RadioGroup
            value={value}
            onValueChange={(val: string) => handleInputChange(question.id, val)}
            required={question.isRequired}
          >
            <div className="space-y-2">
              {question.questionOptions?.map((option, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <RadioGroupItem
                    value={option.label}
                    id={`option-${option.id}`}
                  />
                  <Label
                    htmlFor={`option-${option.id}`}
                    className="cursor-pointer"
                  >
                    {option.label}
                  </Label>
                  {option.sublabel && (
                    <p className="text-sm text-neutral-700 ml-4">
                      {`(${option.sublabel})`}
                    </p>
                  )}
                </div>
              ))}
            </div>
          </RadioGroup>
        );

      case "selectmany":
        return (
          <div className="space-y-2">
            {question.questionOptions?.map((option) => (
              <div key={option.id} className="flex items-center space-x-2">
                <Checkbox
                  className="w-5 h-5 border border-neutral-500"
                  id={`option-${option.id}`}
                  checked={(value || []).includes(option.label)}
                  onCheckedChange={(checked) => {
                    const currentValues = value || [];
                    const newValues = checked
                      ? [...currentValues, option.label]
                      : currentValues.filter((v: string) => v !== option.label);
                    handleInputChange(question.id, newValues);
                  }}
                />
                <Label
                  htmlFor={`option-${option.id}`}
                  className="cursor-pointer"
                >
                  {option.label} {option.sublabel}
                </Label>
              </div>
            ))}
          </div>
        );

      case "date":
        return (
          <div className="relative">
            <Input
              type="date"
              value={value}
              onChange={(e) => handleInputChange(question.id, e.target.value)}
              placeholder={question.hint || t('selectDate')}
              required={question.isRequired}
            />
            <Calendar className="absolute top-3 right-3 h-4 w-4 text-muted-foreground pointer-events-none" />
          </div>
        );

      case "dateandtime":
        return (
          <div className="relative">
            <Input
              type="time"
              value={value}
              onChange={(e) => handleInputChange(question.id, e.target.value)}
              placeholder={question.hint || t('selectTime')}
              required={question.isRequired}
            />
            <Clock className="absolute top-3 right-3 h-4 w-4 text-muted-foreground pointer-events-none" />
          </div>
        );

      case "table":
        return (
          <TableInput
            questionId={question.id}
            value={value}
            onChange={(cellValues) =>
              handleInputChange(question.id, cellValues)
            }
            required={question.isRequired}
            tableLabel={question.label}
          />
        );

      default:
        return null;
    }
  };

  return (
    <div className="bg-neutral-100 dark:bg-neutral-800 rounded-md shadow-sm border border-neutral-500 dark:border-neutral-700">
      <div className="flex items-center justify-between p-4 border-b border-neutral-500 dark:border-neutral-700">
        <Button variant="ghost" size="icon" onClick={onClose}>
          <ChevronLeft className="h-5 w-5" />
        </Button>
        <h2 className="text-lg font-semibold">{t('formPreview')}</h2>
        <Button
          className="cursor-pointer hover:bg-neutral-200"
          variant="ghost"
          size="icon"
          onClick={onClose}
        >
          <X className="h-5 w-5" />
        </Button>
      </div>

      <div className="p-4 md:p-6">
        <div className="space-y-6">
          {/* Render unified form items (groups and individual questions) in order */}
          {unifiedFormItems.map((item) => {
            if (item.type === "group") {
              const group = item.data as QuestionGroup;
              const isExpanded = expandedGroups[group.id];

              // Get questions for this group
              const groupQuestions = questions.filter(q => q.questionGroupId === group.id);
              const visibleGroupQuestions = groupQuestions.filter(gq =>
                visibleQuestions.some(vq => vq.id === gq.id)
              );

              return (
                <div
                  key={`group-${group.id}`}
                  className="border border-neutral-500 dark:border-neutral-600 rounded-lg bg-neutral-100 dark:bg-neutral-800 overflow-hidden"
                >
                  {/* Group Header */}
                  <div
                    className="flex items-center justify-between p-4 border-b border-neutral-500 dark:border-neutral-700 cursor-pointer hover:bg-neutral-200 dark:hover:bg-neutral-700"
                    onClick={() => toggleGroupExpansion(group.id)}
                  >
                    <div className="flex items-center space-x-2">
                      {isExpanded ? (
                        <ChevronDown className="h-5 w-5 text-neutral-500" />
                      ) : (
                        <ChevronRight className="h-5 w-5 text-neutral-500" />
                      )}
                      <h3 className="text-lg font-semibold dark:text-neutral-100">
                        {group.title}
                      </h3>
                      <span className="text-sm text-neutral-700 dark:text-neutral-400">
                        ({visibleGroupQuestions.length} {t('visibleQuestion')}
                        {visibleGroupQuestions.length !== 1 ? t('s') : ""})
                      </span>
                    </div>
                  </div>

                  {/* Group Content with Nested Questions */}
                  {isExpanded && (
                    <div className="p-4 space-y-4">
                      {nestedQuestions
                        .filter((nq) =>
                          groupQuestions.some(
                            (gq) => gq.id === nq.question.id
                          )
                        )
                        .map((questionGroup) => (
                          <NestedQuestionRenderer
                            key={questionGroup.question.id}
                            questionGroup={questionGroup}
                            renderQuestionInput={renderQuestionInput}
                            errors={errors}
                            className=""
                          />
                        ))}
                    </div>
                  )}
                </div>
              );
            } else {
              const question = item.data as Question;
              // Only render ungrouped questions that are visible
              if (!visibleQuestions.some((vq) => vq.id === question.id)) {
                return null;
              }

              // Find the nested question structure for this question
              const nestedQuestion = nestedQuestions.find(
                (nq) => nq.question.id === question.id
              );

              if (nestedQuestion) {
                return (
                  <NestedQuestionRenderer
                    key={question.id}
                    questionGroup={nestedQuestion}
                    renderQuestionInput={renderQuestionInput}
                    errors={errors}
                    className=""
                  />
                );
              }
              return renderQuestion(question);
            }
          })}

          {/* Empty state */}
          {questions.length === 0 && (
            <div className="text-center py-12">
              <p className="text-muted-foreground">
                {t('noFormQuestionsYet')}
              </p>
            </div>
          )}

          {questions.length > 0 && visibleQuestions.length === 0 && (
            <div className="text-center py-12">
              <p className="text-muted-foreground">
                {t('noVisibleQuestions')}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}