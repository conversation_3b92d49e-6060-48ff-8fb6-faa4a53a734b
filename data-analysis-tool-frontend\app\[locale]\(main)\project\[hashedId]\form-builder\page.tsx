"use client";

import { FormPreview } from "@/components/form-preview";
import { useState } from "react";
import { useParams } from "next/navigation";
import { decode } from "@/lib/encodeDecode";
import { FormBuilder } from "@/components/form-builder/FormBuilder";
import { useProjectPermissions } from "@/hooks/useProjectPermissions";
import { fetchProjectById } from "@/lib/api/projects";
import { Project } from "@/types";
import { useAuth } from "@/hooks/useAuth";
import { useTranslations } from "next-intl";
import { useQuery } from "@tanstack/react-query";

export default function FormBuilderPage() {
  const [isPreviewMode, setIsPreviewMode] = useState(false);

  const { hashedId } = useParams();
  const { user } = useAuth();
  const hashedIdString = hashedId as string;

  const projectId = Number(decode(hashedIdString));
  const t = useTranslations();

  // Remove the separate questions query since FormBuilder now handles data loading internally
  // FormPreview also handles its own data loading now

  const {
    data: projectData,
    isLoading: projectLoading,
    isError: projectError,
  } = useQuery<Project>({
    queryKey: ["projects", user?.id, projectId],
    queryFn: () => fetchProjectById({ projectId: projectId! }),
    enabled: !!projectId && !!user?.id,
  });

  const permissions = useProjectPermissions({
    projectData,
    user,
  });

  if (!hashedId || projectId === null) {
    return (
      <div className="error-message">
        <h1 className="text-red-500">{t('invalidProjectId')}</h1>
        <p className="text-neutral-700">
          {t('invalidProjectUrl')}
        </p>
      </div>
    );
  }

  // Loading and error handling is now managed within FormBuilder component

  return (
    <div className="p-6">
      {isPreviewMode ? (
        <FormPreview
          contextType="project"
          contextId={projectId}
          onClose={() => setIsPreviewMode(false)}
          hashedId={hashedIdString} // Pass hashedId for fallback
        />
      ) : (
        <FormBuilder
          setIsPreviewMode={setIsPreviewMode}
          contextType="project"
          contextId={projectId}
          permissions={permissions}
        />
      )}
    </div>
  );
}
